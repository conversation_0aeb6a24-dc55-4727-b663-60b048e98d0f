const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');

const config = require('./config');
const routes = require('./routes');
const { validateRequest, handleValidationError } = require('./middleware/validation');
const { chatRateLimit } = require('./middleware/rateLimit');
const { serve, setup } = require('./swagger/swagger');

// Create Express app
const app = express();

// Trust proxy (for rate limiting and IP detection)
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false, // Disable CSP for API
  crossOriginEmbedderPolicy: false // Allow embedding
}));

// CORS configuration
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);

    // In production, you should whitelist specific domains
    if (config.nodeEnv === 'development') {
      return callback(null, true);
    }

    // Add your allowed origins here
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://your-frontend-domain.com'
    ];

    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    }

    return callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Compression middleware
app.use(compression());

// Logging middleware
if (config.nodeEnv !== 'test') {
  app.use(morgan('combined'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request validation middleware
app.use(validateRequest);

// Apply stricter rate limiting to chat endpoints
app.use('/chat', chatRateLimit);

// Swagger API Documentation
app.use('/api-docs', serve, setup);

// Routes
app.use('/', routes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: true,
    message: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use(handleValidationError);

// Global error handler
app.use((error, req, res, next) => {
  console.error('❌ Unhandled error:', error);

  // Don't send error details in production
  const errorMessage = config.nodeEnv === 'production'
    ? 'Internal server error'
    : error.message;

  if (!res.headersSent) {
    res.status(500).json({
      error: true,
      message: errorMessage,
      timestamp: new Date().toISOString()
    });
  }
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Process terminated');
    process.exit(0);
  });
});

// Start server
const server = app.listen(config.port, () => {
  console.log('🚀 ChatAI SDK Service started successfully!');
  console.log(`📡 Server running on port ${config.port}`);
  console.log(`🌍 Environment: ${config.nodeEnv}`);
  console.log(`🔗 Health check: http://localhost:${config.port}/health`);
  console.log(`📚 API docs: http://localhost:${config.port}/`);
  console.log(`📖 Swagger UI: http://localhost:${config.port}/api-docs`);

  // Log service configurations
  console.log('\n🔧 Service Configuration:');
  console.log(`   LlamaIndex: ${config.llamaIndex.apiKey ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   OpenRouter: ${config.openRouter.apiKey ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   User Service: ${config.userService.url}`);
  console.log(`   Cache TTL: ${config.cache.ttlMinutes} minutes`);
  console.log(`   Rate Limit: ${config.rateLimit.maxRequests} requests per ${config.rateLimit.windowMinutes} minutes`);
});

module.exports = app;
