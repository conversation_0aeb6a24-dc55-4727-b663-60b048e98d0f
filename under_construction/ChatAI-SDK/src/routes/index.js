const express = require('express');
const fetch = require('node-fetch');
const chatController = require('../controllers/chatController');
const { validateChatRequest, validateSessionId, validateEmbedRequest } = require('../middleware/validation');
const rateLimit = require('../middleware/rateLimit');

const router = express.Router();

// Apply rate limiting to all routes
router.use(rateLimit);

/**
 * @swagger
 * /chat/{appId}:
 *   get:
 *     summary: Chat with documents for a specific application
 *     description: Send a query to chat with documents using RAG (Retrieval-Augmented Generation)
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: appId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Application ID
 *       - in: query
 *         name: apiKey
 *         required: false
 *         schema:
 *           type: string
 *         description: Application API key (validation temporarily disabled)
 *       - in: query
 *         name: query
 *         required: true
 *         schema:
 *           type: string
 *         description: User query to ask about the documents
 *         example: "What is the main topic of the documents?"
 *       - in: query
 *         name: sessionId
 *         required: false
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Optional session ID for conversation continuity
 *         example: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
 *       - in: query
 *         name: stream
 *         required: false
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Whether to stream the response
 *       - in: query
 *         name: includeHistory
 *         required: false
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to include chat history in context
 *     responses:
 *       200:
 *         description: Successful chat response (streaming or non-streaming)
 *         content:
 *           text/event-stream:
 *             schema:
 *               $ref: '#/components/schemas/StreamingResponse'
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ChatResponse'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// API v1 route (chat endpoint with API key validation and streaming response)
router.get('/api/v1/', async (req, res) => {
  const { apikey, query, sessionId, stream = 'true', includeHistory = 'false' } = req.query;

  // Both apikey and query are required
  if (!apikey) {
    return res.status(400).json({
      error: true,
      message: 'API key is required'
    });
  }

  if (!query) {
    return res.status(400).json({
      error: true,
      message: 'Query parameter is required'
    });
  }

  if (query.length > 2000) {
    return res.status(400).json({
      error: true,
      message: 'Query is too long (max 2000 characters)'
    });
  }

  // Convert string query parameters to appropriate types
  const streamBool = stream === 'true' || stream === true;
  const includeHistoryBool = includeHistory === 'true' || includeHistory === true;

  try {
    const userService = require('../services/userService');
    const origin = req.headers.origin || req.headers.referer || 'unknown';

    // Step 1: Validate API key through User-Service
    console.log('🔑 Validating API key for /api/v1/ endpoint...');
    const validationResponse = await fetch(
      `${userService.baseUrl}/users/app/key-validator`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': userService.chatAiOrigin
        },
        body: JSON.stringify({
          chainId: 1,
          apikey: apikey,
          origin: origin,
          payload: {
            method: 'validate',
            params: []
          },
          type: 'chatai'
        })
      }
    );

    if (!validationResponse.ok) {
      const errorText = await validationResponse.text();
      throw new Error(`Key validation failed: ${validationResponse.status} - ${errorText}`);
    }

    const validationResult = await validationResponse.json();

    if (validationResult.error) {
      throw new Error(validationResult.message || 'API key validation failed');
    }

    // Extract data from validation result
    const chatAiData = validationResult.result;
    const appId = chatAiData.appId;
    const chatAiId = chatAiData.id;

    if (!appId || !chatAiId) {
      throw new Error('Invalid API key: missing appId or chatAiId');
    }

    console.log(`✅ API key validated successfully. AppId: ${appId}, ChatAiId: ${chatAiId}`);

    // Step 2: Import required services
    const llamaIndexService = require('../services/llamaIndexService');
    const openRouterService = require('../services/openRouterService');
    const cacheService = require('../services/cacheService');
    const chatController = require('../controllers/chatController');

    // Step 3: Get or create session using appId (for compatibility with existing cache logic)
    const currentSessionId = cacheService.getOrCreateSession(appId, sessionId);

    // Step 4: Get documents using appId (reuse existing logic)
    const documents = await chatController.getDocumentsForChat(currentSessionId, appId, null); // No auth token needed

    if (!documents || documents.length === 0) {
      return res.status(404).json({
        error: true,
        message: 'No ready documents found for this API key'
      });
    }

    console.log(`📋 Found ${documents.length} ready documents for processing`);

    // Step 5: Retrieve context from LlamaIndex using documents
    console.log('🔍 Retrieving context from LlamaIndex...');
    const context = await llamaIndexService.retrieveFromMultipleDocuments(documents, query);

    // Step 6: Generate and stream response
    if (streamBool) {
      console.log('📡 Starting streaming response...');
      await chatController.streamChatResponse(res, query, context, currentSessionId);
    } else {
      console.log('💬 Generating non-streaming response...');
      const response = await openRouterService.generateResponse(query, context);

      res.json({
        error: false,
        sessionId: currentSessionId,
        response,
        documentsUsed: documents.length,
        contextLength: context.length
      });
    }

  } catch (error) {
    console.error('❌ API v1 chat error:', error.message);

    // Return error in JSON format if headers not sent yet
    if (!res.headersSent) {
      const statusCode = error.message.includes('validation failed') ? 403 :
        error.message.includes('not found') ? 404 : 500;

      return res.status(statusCode).json({
        error: true,
        message: error.message
      });
    }
  }
});

// Chat routes
router.get('/chat/:appId', validateChatRequest, chatController.chat.bind(chatController));

/**
 * @swagger
 * /widget-chat/{appId}:
 *   get:
 *     summary: Chat endpoint for widget (no authentication required)
 *     description: Chat endpoint specifically for iframe widgets without authentication requirement
 *     tags: [Widget]
 *     parameters:
 *       - in: path
 *         name: appId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Application ID
 *       - in: query
 *         name: query
 *         required: true
 *         schema:
 *           type: string
 *           maxLength: 2000
 *         description: User's chat query
 *       - in: query
 *         name: sessionId
 *         required: false
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Session ID for conversation continuity
 *       - in: query
 *         name: stream
 *         required: false
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *           default: 'false'
 *         description: Whether to stream the response
 *       - in: query
 *         name: includeHistory
 *         required: false
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *           default: 'false'
 *         description: Whether to include conversation history
 *     responses:
 *       200:
 *         description: Chat response generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ChatResponse'
 *           text/event-stream:
 *             schema:
 *               type: string
 *               description: Server-sent events stream for real-time chat
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/widget-chat/:appId', chatController.widgetChat.bind(chatController));

/**
 * @swagger
 * /embed/{appId}:
 *   get:
 *     summary: Get iframe URL for chat widget
 *     description: Returns an iframe URL that can be embedded on websites for chat functionality
 *     tags: [Embed]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: appId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Application ID
 *       - in: query
 *         name: theme
 *         required: false
 *         schema:
 *           type: string
 *           enum: ['light', 'dark']
 *           default: 'light'
 *         description: Theme for the chat widget
 *       - in: query
 *         name: width
 *         required: false
 *         schema:
 *           type: string
 *           default: '400px'
 *         description: Width of the chat widget
 *       - in: query
 *         name: height
 *         required: false
 *         schema:
 *           type: string
 *           default: '600px'
 *         description: Height of the chat widget
 *     responses:
 *       200:
 *         description: Iframe URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 iframeUrl:
 *                   type: string
 *                   description: URL for the chat iframe
 *                   example: "http://localhost:3001/chat-widget?appId=123e4567-e89b-12d3-a456-426614174000&theme=light"
 *                 embedCode:
 *                   type: string
 *                   description: Ready-to-use HTML iframe code
 *                   example: '<iframe src="http://localhost:3001/chat-widget?appId=123e4567-e89b-12d3-a456-426614174000" width="400px" height="600px" frameborder="0"></iframe>'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/embed/:appId', validateEmbedRequest, chatController.getEmbedUrl.bind(chatController));

/**
 * @swagger
 * /chat-widget:
 *   get:
 *     summary: Chat widget HTML page
 *     description: Serves the embeddable chat widget HTML page
 *     tags: [Embed]
 *     parameters:
 *       - in: query
 *         name: appId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Application ID
 *       - in: query
 *         name: theme
 *         required: false
 *         schema:
 *           type: string
 *           enum: ['light', 'dark']
 *           default: 'light'
 *         description: Theme for the chat widget
 *     responses:
 *       200:
 *         description: Chat widget HTML page
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *               description: HTML page for the chat widget
 */
router.get('/chat-widget', chatController.serveChatWidget.bind(chatController));

/**
 * @swagger
 * /session/{sessionId}:
 *   get:
 *     summary: Get session information
 *     description: Retrieve information about a specific chat session
 *     tags: [Session Management]
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Session ID
 *     responses:
 *       200:
 *         description: Session information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SessionInfo'
 *       404:
 *         description: Session not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *   delete:
 *     summary: Invalidate session
 *     description: Delete and invalidate a specific chat session
 *     tags: [Session Management]
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Session ID
 *     responses:
 *       200:
 *         description: Session invalidated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Session invalidated successfully"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Session management routes
router.get('/session/:sessionId', validateSessionId, chatController.getSession.bind(chatController));
router.delete('/session/:sessionId', validateSessionId, chatController.invalidateSession.bind(chatController));

/**
 * @swagger
 * /stats:
 *   get:
 *     summary: Get service statistics
 *     description: Retrieve cache and service statistics
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ServiceStats'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check
 *     description: Check the health status of the service and its dependencies
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthCheck'
 *       500:
 *         description: Service is unhealthy
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Utility routes
router.get('/stats', chatController.getStats.bind(chatController));
router.get('/health', chatController.healthCheck.bind(chatController));

// API documentation endpoint
router.get('/', (req, res) => {
  res.json({
    name: 'ChatAI SDK Service',
    version: '1.0.0',
    description: 'Express SDK service for ChatAI with LlamaIndex and OpenRouter integration',
    endpoints: {
      'GET /chat/:appId': {
        description: 'Chat with documents for a specific app',
        parameters: {
          appId: 'Application ID (path parameter)',
          apiKey: 'Application API key (query parameter, validation temporarily disabled)',
          query: 'User query (query parameter)',
          sessionId: 'Optional session ID (body)',
          stream: 'Enable streaming response (body, default: true)',
          includeHistory: 'Include chat history (body, default: false)'
        },
        headers: {
          'Authorization': 'Bearer <jwt-token>'
        }
      },
      'GET /session/:sessionId': {
        description: 'Get session information',
        parameters: {
          sessionId: 'Session ID (path parameter)'
        }
      },
      'DELETE /session/:sessionId': {
        description: 'Invalidate a session',
        parameters: {
          sessionId: 'Session ID (path parameter)'
        }
      },
      'GET /stats': {
        description: 'Get cache and service statistics'
      },
      'GET /health': {
        description: 'Health check endpoint'
      }
    },
    examples: {
      chat: {
        url: 'GET /chat/your-app-id?apiKey=your-api-key&query=What is the main topic?&stream=true',
        headers: {
          'Authorization': 'Bearer your-jwt-token'
        },
        queryParams: {
          query: 'What is the main topic of the documents?',
          sessionId: 'optional-session-id',
          stream: true
        },
        note: 'apiKey validation is temporarily disabled for development'
      },
      streaming_response: {
        description: 'Server-Sent Events format',
        events: [
          'data: {"type":"session","sessionId":"uuid","timestamp":"2024-01-01T00:00:00.000Z"}',
          'data: {"type":"content","content":"The main topic"}',
          'data: {"type":"content","content":" of the documents is..."}',
          'data: {"type":"done","timestamp":"2024-01-01T00:00:01.000Z"}'
        ]
      }
    }
  });
});

module.exports = router;
