const fetch = require('node-fetch');
const config = require('../config');

class UserService {
  constructor() {
    this.baseUrl = config.userService.url;
    this.chatAiOrigin = config.chatAiOrigin;
  }

  /**
   * Fetch documents for a given appId
   * @param {string} appId - Application ID
   * @param {string} authToken - JWT token for authentication
   * @returns {Promise<Array>} Array of documents with retriever IDs
   */
  async getDocuments(appId, authToken) {
    try {
      console.log(`📋 Fetching documents for appId: ${appId}`);

      const headers = {
        'Content-Type': 'application/json',
      };

      // Add authorization header only if authToken is provided
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }

      // Use widget endpoint if no auth token provided
      const endpoint = authToken
        ? `/users/app/chatai/get-documents?appId=${appId}`
        : `/users/app/chatai/widget/get-documents?appId=${appId}`;

      const response = await fetch(
        `${this.baseUrl}${endpoint}`,
        {
          method: 'GET',
          headers: headers,
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`User Service error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.message || 'Failed to fetch documents');
      }

      // Filter ready documents with valid retriever IDs
      const readyDocuments = (result.result || []).filter(doc =>
        doc.status === 'ready' &&
        doc.indexId &&
        !doc.indexId.startsWith('fallback-')
      );

      console.log(`✅ Found ${readyDocuments.length} ready documents`);

      // Log document details for debugging
      readyDocuments.forEach(doc => {
        console.log(`📄 Document: ${doc.filename}, indexId: ${doc.indexId}, hasText: ${!!doc.parsedData?.text}`);
      });

      return readyDocuments;

    } catch (error) {
      console.error('❌ UserService.getDocuments error:', error.message);
      throw error;
    }
  }

  /**
   * Validate API key and check credits using User-Service key-validator
   * @param {string} apiKey - Application API key
   * @param {string} origin - Request origin
   * @param {string} appId - Application ID
   * @returns {Promise<Object>} Validation result with credit info
   */
  async validateApiKey(apiKey, origin, appId) {
    try {
      console.log(`🔑 Validating API key for appId: ${appId}`);

      const response = await fetch(
        `${this.baseUrl}/users/app/key-validator`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Origin': this.chatAiOrigin
          },
          body: JSON.stringify({
            chainId: 1, // Default chainId for ChatAI
            apikey: apiKey,
            origin: origin,
            payload: {
              method: 'chat',
              params: [appId]
            },
            type: 'chatai'
          })
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Key validation failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.message || 'API key validation failed');
      }

      console.log(`✅ API key validated. Credits: ${result.result.creditInfo?.creditsRemaining}`);
      return result.result;

    } catch (error) {
      console.error('❌ UserService.validateApiKey error:', error.message);
      throw error;
    }
  }

  /**
   * Validate if appId exists and user has access
   * @param {string} appId - Application ID
   * @param {string} authToken - JWT token for authentication
   * @returns {Promise<boolean>} True if valid
   */
  async validateAppId(appId, authToken) {
    try {
      console.log(`🔍 Validating appId: ${appId}`);

      const headers = {
        'Content-Type': 'application/json',
      };

      // Add authorization header only if authToken is provided
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }

      // Use widget endpoint if no auth token provided
      const endpoint = authToken
        ? `/users/app/chatai/get-single-chatai?appId=${appId}`
        : `/users/app/chatai/widget/get-single-chatai?appId=${appId}`;

      const response = await fetch(
        `${this.baseUrl}${endpoint}`,
        {
          method: 'GET',
          headers: headers,
        }
      );

      if (!response.ok) {
        return false;
      }

      const result = await response.json();
      return !result.error;

    } catch (error) {
      console.error('❌ UserService.validateAppId error:', error.message);
      return false;
    }
  }
}

module.exports = new UserService();
