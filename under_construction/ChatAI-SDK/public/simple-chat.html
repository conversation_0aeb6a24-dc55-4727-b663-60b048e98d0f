<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatAI Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            height: calc(100vh - 40px);
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e1e8ed;
            text-align: center;
            background: #3498db;
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 85%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .message.user {
            align-self: flex-end;
            background: #3498db;
            color: white;
        }

        .message.bot {
            align-self: flex-start;
            background: #f1f3f4;
            color: #333;
            border: 1px solid #e1e8ed;
        }

        .message.typing {
            align-self: flex-start;
            background: #f1f3f4;
            color: #666;
            font-style: italic;
        }

        .typing-indicator {
            display: inline-flex;
            gap: 4px;
            align-items: center;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #666;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {

            0%,
            60%,
            100% {
                opacity: 0.3;
            }

            30% {
                opacity: 1;
            }
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #e1e8ed;
            background: #fafbfc;
            border-radius: 0 0 12px 12px;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-field {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 24px;
            background: white;
            font-size: 16px;
            resize: none;
            min-height: 48px;
            max-height: 120px;
            outline: none;
            transition: border-color 0.3s;
        }

        .input-field:focus {
            border-color: #3498db;
        }

        .send-button {
            padding: 12px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 24px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
            min-width: 80px;
        }

        .send-button:hover:not(:disabled) {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .send-button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .welcome-message {
            text-align: center;
            color: #7f8c8d;
            padding: 40px 20px;
            font-size: 16px;
        }

        .error-message {
            background: #e74c3c;
            color: white;
            padding: 12px 16px;
            border-radius: 18px;
            align-self: flex-start;
            max-width: 85%;
        }

        .performance-info {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 8px;
            padding: 8px 12px;
            background: #ecf0f1;
            border-radius: 12px;
            align-self: flex-start;
        }

        .cache-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            margin-left: 8px;
        }

        .cache-hit {
            background: #d4edda;
            color: #155724;
        }

        .cache-miss {
            background: #f8d7da;
            color: #721c24;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .chat-container {
                margin: 10px;
                height: calc(100vh - 20px);
                border-radius: 8px;
            }

            .chat-header {
                padding: 15px;
                border-radius: 8px 8px 0 0;
            }

            .chat-header h1 {
                font-size: 20px;
            }

            .chat-messages {
                padding: 15px;
            }

            .chat-input {
                padding: 15px;
                border-radius: 0 0 8px 8px;
            }

            .input-field {
                font-size: 16px;
                /* Prevent zoom on iOS */
            }
        }
    </style>
</head>

<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>💬 ChatAI Test</h1>
            <p>Ask questions about your documents</p>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                👋 Hello! Ask me anything about your documents.<br>
                <small>Try: "What is the invoice amount?" or "When is the due date?"</small>
            </div>
        </div>

        <div class="chat-input">
            <div class="input-container">
                <textarea class="input-field" id="messageInput" placeholder="Type your message..." rows="1"></textarea>
                <button class="send-button" id="sendButton">Send</button>
            </div>
        </div>
    </div>

    <script>
        class SimpleChatUI {
            constructor() {
                this.apiKey = 'test_api_key_1751884336144_vp9gospvg';
                this.baseUrl = 'http://localhost:3001';
                this.sessionId = null;
                this.isLoading = false;

                this.initializeElements();
                this.bindEvents();
            }

            initializeElements() {
                this.messagesContainer = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
            }

            bindEvents() {
                this.sendButton.addEventListener('click', () => this.sendMessage());

                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // Auto-resize textarea
                this.messageInput.addEventListener('input', () => {
                    this.messageInput.style.height = 'auto';
                    this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
                });
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.isLoading) return;

                // Add user message
                this.addMessage(message, 'user');
                this.messageInput.value = '';
                this.messageInput.style.height = 'auto';

                // Show typing indicator
                this.setLoading(true);

                try {
                    const startTime = Date.now();
                    await this.callStreamingAPI(message, startTime);
                } catch (error) {
                    this.addMessage(`Error: ${error.message}`, 'error');
                    console.error('Chat error:', error);
                } finally {
                    this.setLoading(false);
                }
            }

            async callStreamingAPI(message, startTime) {
                const params = new URLSearchParams({
                    apikey: this.apiKey,
                    query: message,
                    stream: 'true',
                    includeHistory: 'false'
                });

                if (this.sessionId) {
                    params.append('sessionId', this.sessionId);
                }

                const url = `${this.baseUrl}/api/v1/?${params.toString()}`;

                const response = await fetch(url, {
                    headers: {
                        'Origin': 'http://localhost:3001'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                await this.handleStreamingResponse(response, startTime);
            }

            async handleStreamingResponse(response, startTime) {
                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                let botMessageDiv = null;
                let fullResponse = '';
                let firstChunkTime = null;
                let streamEndTime = null;
                let timingData = null;

                try {
                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) break;

                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));

                                    if (data.type === 'content') {
                                        if (!firstChunkTime) {
                                            firstChunkTime = Date.now();
                                            // Remove typing indicator and create bot message
                                            this.removeTypingIndicator();
                                            botMessageDiv = this.createBotMessage();
                                        }

                                        fullResponse += data.content;
                                        this.updateBotMessage(botMessageDiv, fullResponse);

                                    } else if (data.type === 'session') {
                                        this.sessionId = data.sessionId;

                                    } else if (data.type === 'done') {
                                        streamEndTime = Date.now();
                                        timingData = data.timing;

                                        // Add performance info
                                        if (timingData) {
                                            const totalTime = streamEndTime - startTime;
                                            this.addPerformanceInfo(timingData, null, totalTime);
                                        }

                                    } else if (data.type === 'error') {
                                        this.addMessage(`Error: ${data.message}`, 'error');
                                    }
                                } catch (e) {
                                    console.warn('Failed to parse SSE data:', line);
                                }
                            }
                        }
                    }
                } catch (error) {
                    if (botMessageDiv) {
                        this.updateBotMessage(botMessageDiv, fullResponse + '\n\n[Stream interrupted]');
                    }
                    throw error;
                }
            }

            handleResponse(data, totalTime) {
                if (data.error) {
                    this.addMessage(data.message || 'An error occurred', 'error');
                    return;
                }

                this.sessionId = data.sessionId;

                // Add bot response
                this.addMessage(data.response, 'bot');

                // Add performance info if available
                if (data.timing || data.cached) {
                    this.addPerformanceInfo(data.timing, data.cached, totalTime);
                }
            }

            addMessage(content, type) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.textContent = content;

                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
            }

            createBotMessage() {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message bot';
                messageDiv.textContent = '';

                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
                return messageDiv;
            }

            updateBotMessage(messageDiv, content) {
                messageDiv.textContent = content;
                this.scrollToBottom();
            }

            addPerformanceInfo(timing, cached, totalTime) {
                const perfDiv = document.createElement('div');
                perfDiv.className = 'performance-info';

                let perfText = `⏱️ ${totalTime}ms total`;

                if (cached) {
                    if (cached.apiKey) {
                        perfText += `<span class="cache-badge cache-hit">API ✓</span>`;
                    } else {
                        perfText += `<span class="cache-badge cache-miss">API ✗</span>`;
                    }

                    if (cached.context) {
                        perfText += `<span class="cache-badge cache-hit">Context ✓</span>`;
                    } else {
                        perfText += `<span class="cache-badge cache-miss">Context ✗</span>`;
                    }
                }

                perfDiv.innerHTML = perfText;
                this.messagesContainer.appendChild(perfDiv);
                this.scrollToBottom();
            }

            setLoading(loading) {
                this.isLoading = loading;
                this.sendButton.disabled = loading;
                this.sendButton.textContent = loading ? '...' : 'Send';

                if (loading) {
                    this.addTypingIndicator();
                } else {
                    this.removeTypingIndicator();
                }
            }

            addTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'message typing';
                typingDiv.id = 'typingIndicator';
                typingDiv.innerHTML = `
                    <div class="typing-indicator">
                        Thinking
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                `;

                this.messagesContainer.appendChild(typingDiv);
                this.scrollToBottom();
            }

            removeTypingIndicator() {
                const typingIndicator = document.getElementById('typingIndicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            scrollToBottom() {
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }
        }

        // Initialize chat when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleChatUI();
        });
    </script>
</body>

</html>